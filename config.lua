Config = {}

Config.oldESX = true

Config.DrawDistance = 10.0

Config.marker = {type = 1, x = 2.0, y = 2.0, z = 1.5, r = 250, g = 0, b = 0}  -- 更大的标记

Config.Locale = 'zh'

-- 大标记指示帮派斗争期间的斗争区域
Config.DrawDistance2 = 100.0

Config.marker2 = {type = 1, r = 250, g = 0, b = 0}

Config.safepos = vec3(216.4, -786.6, 30.8)

-- 测试模式：设为true时跳过在线成员检查
Config.testMode = false

-- 帮派职业配置 - 只有这些职业可以发起和接受帮派斗争
Config.GangJobs = {
    'ballas',       
    'families',     
    'vagos',        
    'marabunta',    
    'triads',       
    'cartel',      
    'police'
}


-- 帮派斗争地点配置
Config.fightZones = {

    ['格罗夫街地盘'] = {
        coords = vec3(-127.8, -1737.1, 30.1),
        minGangMembers = 0,  -- 除了自己最少需要的帮派成员数量
        money1 = 150000,
        money2 = 200000,
        maxDist = 50.0,
        cd = 3600,  -- 冷却时间（秒）
        description = '格罗夫街家族的核心地盘'
    },

    

    ['巴拉斯地盘'] = {
        coords = vec3(105.2, -1885.4, 24.3),
        minGangMembers = 2,
        money1 = 150000,
        money2 = 200000,
        maxDist = 50.0,
        cd = 3600,
        description = '巴拉斯帮的传统领地'
    },

    ['瓦戈斯地盘'] = {
        coords = vec3(331.3, -2012.0, 22.4),
        minGangMembers = 2,
        money1 = 150000,
        money2 = 200000,
        maxDist = 50.0,
        cd = 3600,
        description = '瓦戈斯帮的控制区域'
    },

    ['码头争夺点'] = {
        coords = vec3(1207.7, -3113.2, 5.5),
        minGangMembers = 3,
        money1 = 200000,
        money2 = 300000,
        maxDist = 60.0,
        cd = 3600,
        description = '重要的货物运输枢纽'
    },

    ['工业区争夺点'] = {
        coords = vec3(716.9, -962.1, 30.4),
        minGangMembers = 2,
        money1 = 180000,
        money2 = 250000,
        maxDist = 45.0,
        cd = 3600,
        description = '工业区的战略要地'
    },

    ['市中心争夺点'] = {
        coords = vec3(-47.5, -1757.5, 29.4),
        minGangMembers = 4,
        money1 = 250000,
        money2 = 400000,
        maxDist = 80.0,  -- 增加到80米范围
        cd = 3600,
        description = '市中心的黄金地段'
    },

    ['海滩争夺点'] = {
        coords = vec3(-1388.1, -1279.8, 4.6),
        minGangMembers = 2,
        money1 = 120000,
        money2 = 180000,
        maxDist = 55.0,
        cd = 3600,
        description = '海滩娱乐区域'
    },

    ['山区据点'] = {
        coords = vec3(-1119.3, 2698.2, 18.6),
        minGangMembers = 3,
        money1 = 200000,
        money2 = 350000,
        maxDist = 70.0,
        cd = 3600,
        description = '偏远但重要的山区据点'
    }
}

