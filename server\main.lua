local cds = {}
local fighting = false
local fightSource = {}
local allInitiators = {} -- 记录所有参与的发起方成员（包括被击毙的）
local allAcceptors = {} -- 记录所有参与的接受方成员（包括被击毙的）
local mainSource = nil

if Config.oldESX then
    ESX = exports["es_extended"]:getSharedObject()
end


-- 检查玩家是否是帮派成员
function IsGangMember(xPlayer)
    if not xPlayer or not xPlayer.job then
        return false
    end

    for _, job in ipairs(Config.GangJobs) do
        if xPlayer.job.name == job then
            return true
        end
    end
    return false
end

-- 获取玩家的帮派名称
function GetPlayerGang(xPlayer)
    if not xPlayer or not xPlayer.job then
        return nil
    end

    for _, job in ipairs(Config.GangJobs) do
        if xPlayer.job.name == job then
            return job
        end
    end
    return nil
end


ESX.RegisterServerCallback('Rs_gangfight:requestStart', function(source, cb, zone)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        cb(false)
        return
    end

    -- 检查是否是帮派成员
    if not IsGangMember(xPlayer) then
        TriggerClientEvent('esx:showNotification', source, _U('not_gang_member'))
        cb(false)
        return
    end

    local time = os.time()
    local xPlayers = ESX.GetPlayers()
    local gangMembers = 0
    local playerGang = GetPlayerGang(xPlayer)

    -- 统计在线帮派成员数量（不包括发起者的帮派）
    for i=1, #xPlayers, 1 do
        local otherPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if otherPlayer ~= nil then
            local otherGang = GetPlayerGang(otherPlayer)
            if otherGang and otherGang ~= playerGang then
                gangMembers = gangMembers + 1
            end
        end
    end

    -- 测试模式下跳过成员检查
    if not Config.testMode and gangMembers < Config.fightZones[zone].minGangMembers then
        TriggerClientEvent('esx:showNotification', source, _U('not_enough_gang') .. ' (需要' .. Config.fightZones[zone].minGangMembers .. '个其他帮派成员在线，当前' .. gangMembers .. '个)')
        cb(false)
    elseif fighting then
        TriggerClientEvent('esx:showNotification', source, _U('fighting'))
        cb(false)
    elseif cds[zone] and time < cds[zone] then
        TriggerClientEvent('esx:showNotification', source, _U('cd_time', os.date('%X', tostring(cds[zone]):sub(1, 10))))
        cb(false)
    else
        cb(true)
    end
end)

ESX.RegisterServerCallback('Rs_gangfight:checkId', function(source, cb, input, zone)
    print("DEBUG: checkId 回调被调用")
    print("DEBUG: source =", source)
    print("DEBUG: input =", input)
    print("DEBUG: zone =", zone)

    if fighting then
        TriggerClientEvent('esx:showNotification', source, _U('fighting'))
        cb(false)
        return
    end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        cb(false)
        return
    end

    -- 检查是否是帮派成员
    if not IsGangMember(xPlayer) then
        TriggerClientEvent('esx:showNotification', source, _U('not_gang_member'))
        cb(false)
        return
    end

    local playerGang = GetPlayerGang(xPlayer)

    -- 验证输入的ID并检查帮派
    local ids = mysplit(input, ',')
    local parti = {}

    -- 检查输入是否为空或无效
    if not ids or #ids == 0 then
        TriggerClientEvent('esx:showNotification', source, '系统错误：输入为空')
        cb(false)
        return
    end

    -- 第一个ID是发起者自己，其余是队友
    local initiatorId = tonumber(ids[1])
    if not initiatorId or initiatorId ~= source then
        TriggerClientEvent('esx:showNotification', source, '系统错误：发起者ID不匹配')
        cb(false)
        return
    end

    -- 将发起者加入队伍
    table.insert(parti, initiatorId)

    -- 检查其余队友ID
    for i = 2, #ids do
        local id = tonumber(ids[i])
        local targetPlayer = ESX.GetPlayerFromId(id)
        if targetPlayer then
            -- 检查是否重复ID
            for n = 1, #parti do
                if id == parti[n] then
                    TriggerClientEvent('esx:showNotification', source, _U('repeat_id'))
                    cb(false)
                    return
                end
            end

            -- 检查目标玩家是否是帮派成员
            if not IsGangMember(targetPlayer) then
                TriggerClientEvent('esx:showNotification', source, _U('gang_member_only'))
                cb(false)
                return
            end

            -- 检查是否是同一帮派（队友必须是同帮派）
            local targetGang = GetPlayerGang(targetPlayer)
            if targetGang ~= playerGang then
                TriggerClientEvent('esx:showNotification', source, '队友必须是同帮派成员')
                cb(false)
                return
            end

            table.insert(parti, id)
        else
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            cb(false)
            return
        end
    end

    -- 开始帮派斗争
    cb(true)
    fighting = zone

	TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, _U('fight_chat_content', zone, input))
	TriggerClientEvent('Rs_gangfight:allStart', -1, zone, parti)
    fightSource['initiator'] = parti
    allInitiators = {} -- 重置记录
    for i = 1, #parti do
        table.insert(allInitiators, parti[i]) -- 记录所有参与的发起方成员
    end
    print("DEBUG: 帮派斗争开始，地点:", zone)
    print("DEBUG: 发起方列表:", json.encode(parti))
    print("DEBUG: allInitiators:", json.encode(allInitiators))
    print("DEBUG: fightSource:", json.encode(fightSource))
    mainSource = source
end)

RegisterNetEvent('Rs_gangfight:acceptorId')
AddEventHandler('Rs_gangfight:acceptorId', function(input)
    local source = source -- 确保获取触发事件的玩家ID
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        return
    end

    -- 检查是否已经有斗争在进行
    if not fighting then
        TriggerClientEvent('esx:showNotification', source, '当前没有进行中的帮派斗争')
        return
    end

    -- 检查是否是帮派成员
    if not IsGangMember(xPlayer) then
        TriggerClientEvent('esx:showNotification', source, _U('gang_member_only'))
        return
    end

    -- 检查是否已经是发起方成员
    if fightSource['initiator'] then
        for i = 1, #fightSource['initiator'] do
            if fightSource['initiator'][i] == source then
                TriggerClientEvent('esx:showNotification', source, '您已经是发起方成员，不能同时成为接受方')
                return
            end
        end
    end

    local playerGang = GetPlayerGang(xPlayer)
    local initiatorGang = nil

    -- 获取发起方的帮派
    if fightSource['initiator'] and #fightSource['initiator'] > 0 then
        local initiatorPlayer = ESX.GetPlayerFromId(fightSource['initiator'][1])
        if initiatorPlayer then
            initiatorGang = GetPlayerGang(initiatorPlayer)
        end
    end

    -- 检查是否与发起方是不同帮派
    if initiatorGang and playerGang == initiatorGang then
        TriggerClientEvent('esx:showNotification', source, '不能接受同帮派的斗争挑战')
        return
    end

    local ids = mysplit(input, ',')
    local parti = {}

    for i = 1, #ids do
        local id = tonumber(ids[i])
        local targetPlayer = ESX.GetPlayerFromId(id)

        -- 先检查targetPlayer是否存在
        if not targetPlayer then
            TriggerClientEvent('esx:showNotification', source, _U('cannot_find_id', id))
            return
        end

        -- 检查是否是发起方成员
        if fightSource['initiator'] then
            for j = 1, #fightSource['initiator'] do
                if fightSource['initiator'][j] == id then
                    TriggerClientEvent('esx:showNotification', source, '不能选择发起方成员作为接受方')
                    return
                end
            end
        end

        -- 检查是否是帮派成员
        if not IsGangMember(targetPlayer) then
            TriggerClientEvent('esx:showNotification', source, _U('gang_member_only'))
            return
        end

        -- 检查是否是同一帮派（接受方必须是同帮派）
        local targetGang = GetPlayerGang(targetPlayer)
        if targetGang ~= playerGang then
            TriggerClientEvent('esx:showNotification', source, '接受方成员必须是同帮派')
            return
        end

        for n = 1, #parti do
            if id == parti[n] then
                TriggerClientEvent('esx:showNotification', source, _U('repeat_id'))
                return
            end
        end

        table.insert(parti, id)
    end

    -- 通知所有相关帮派成员
    local xPlayers = ESX.GetPlayers()
    for i=1, #xPlayers, 1 do
        local otherPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if otherPlayer and IsGangMember(otherPlayer) then
            local otherGang = GetPlayerGang(otherPlayer)
            if otherGang == playerGang then
                TriggerClientEvent('Rs_gangfight:acceptorIdEnd', otherPlayer.source, parti)
            end
        end
    end

    TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, _U('fight_chat_acceptor_content', fighting, input))
    fightSource['acceptor'] = parti
    allAcceptors = {} -- 重置记录
    for i = 1, #parti do
        table.insert(allAcceptors, parti[i]) -- 记录所有参与的接受方成员
    end
    print("DEBUG: 接受方列表:", json.encode(parti))
    print("DEBUG: allAcceptors:", json.encode(allAcceptors))
end)



RegisterNetEvent('Rs_gangfight:out')
AddEventHandler('Rs_gangfight:out', function(killer)
    -- 防止斗争已经结束后继续处理
    if not fighting then
        print("DEBUG: 帮派斗争已结束，忽略out事件")
        return
    end

    print("DEBUG: Rs_gangfight:out 事件触发，source:", source, "fighting:", fighting)
    print("DEBUG: 当前allInitiators:", json.encode(allInitiators))
    print("DEBUG: 当前fightSource:", json.encode(fightSource))

    local playerRemoved = false

    for k, v in pairs(fightSource) do
        print("DEBUG: 检查", k, "组，当前人数:", #v)
        for i = 1, #v do
            if v[i] == source then
                print("DEBUG: 从", k, "组移除玩家", source)
                table.remove(v, i)
                playerRemoved = true
                break
            end
        end
        print("DEBUG: ", k, "组移除后人数:", #v)
        if #v == 0 and playerRemoved then
            print("DEBUG: ", k, "组全部淘汰，帮派斗争结束")

            -- 保存allInitiators和allAcceptors的副本，防止被清空
            local savedInitiators = {}
            for i = 1, #allInitiators do
                table.insert(savedInitiators, allInitiators[i])
            end

            local savedAcceptors = {}
            for i = 1, #allAcceptors do
                table.insert(savedAcceptors, allAcceptors[i])
            end

            print("DEBUG: savedInitiators:", json.encode(savedInitiators))
            print("DEBUG: savedAcceptors:", json.encode(savedAcceptors))

	        TriggerClientEvent('Rs_gangfight:allEnd', -1)
            local winner = '接受方'

            -- 计算奖励金额，每个成员都获得完整金额（包括被击毙的）
            local money1 = Config.fightZones[fighting].money1
            local money2 = Config.fightZones[fighting].money2
            local rewardPerMember
            if money1 == money2 then
                rewardPerMember = money1
            elseif money1 > money2 then
                rewardPerMember = math.random(money2, money1)  -- 交换顺序
            else
                rewardPerMember = math.random(money1, money2)  -- 正常顺序
            end
            print("DEBUG: 每人奖励金额:", rewardPerMember)

            if k == 'acceptor' then
                winner = '发起方'
                print("DEBUG: 发起方获胜，开始分配奖励")

                -- 给所有参与的发起方成员分配奖励（包括被击毙的）
                for i = 1, #savedInitiators do
                    local memberId = savedInitiators[i]
                    local xPlayer = ESX.GetPlayerFromId(memberId)
                    print("DEBUG: 处理发起方成员ID:", memberId, "xPlayer存在:", xPlayer ~= nil)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerMember)
                        xPlayer.showNotification(_U('fight_end', rewardPerMember))
                        print("DEBUG: 给发起方成员", memberId, "发放奖励", rewardPerMember)
                    else
                        print("DEBUG: 发起方成员", memberId, "的xPlayer为nil")
                    end
                end
            else
                print("DEBUG: 接受方获胜，开始分配奖励")

                -- 给所有参与的接受方成员分配奖励（包括被击毙的）
                for i = 1, #savedAcceptors do
                    local memberId = savedAcceptors[i]
                    local xPlayer = ESX.GetPlayerFromId(memberId)
                    print("DEBUG: 处理接受方成员ID:", memberId, "xPlayer存在:", xPlayer ~= nil)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerMember)
                        xPlayer.showNotification(_U('fight_end', rewardPerMember))
                        print("DEBUG: 给接受方成员", memberId, "发放奖励", rewardPerMember)
                    else
                        print("DEBUG: 接受方成员", memberId, "的xPlayer为nil")
                    end
                end
            end

            -- 无论哪方获胜，都设置冷却时间
            cds[fighting] = (os.time() + Config.fightZones[fighting].cd)
            TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, _U('fight_chat_end_content', fighting, winner))
            fighting = false
            fightSource = {}
            allInitiators = {} -- 清空记录
            allAcceptors = {} -- 清空记录
            mainSource = nil
            break
        end
    end
end)



RegisterNetEvent('Rs_gangfight:earlyExit')
AddEventHandler('Rs_gangfight:earlyExit', function()
    TriggerClientEvent('Rs_gangfight:allEnd', -1)
    TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, _U('fight_chat_cancel_content', fighting))
    cds[fighting] = (os.time() + Config.fightZones[fighting].cd)
    fighting = false
    fightSource = {}
    allInitiators = {} -- 清空记录
    allAcceptors = {} -- 清空记录
    mainSource = nil
end)

AddEventHandler('playerDropped', function()
    for k, v in pairs(fightSource) do
        for i = 1, #v do
            if v[i] == source then
                table.remove(v, i)
                break
            end
        end
        if #v == 0 then
	        TriggerClientEvent('Rs_gangfight:allEnd', -1)
            local winner = '接受方'

            -- 计算奖励金额，每个成员都获得完整金额（包括被击毙的）
            local rewardPerMember = math.random(Config.fightZones[fighting].money1, Config.fightZones[fighting].money2)

            if k == 'acceptor' then
                winner = '发起方'
                print("DEBUG: 发起方获胜（玩家掉线），开始分配奖励")

                -- 给所有参与的发起方成员分配奖励（包括被击毙的）
                for i = 1, #allInitiators do
                    local memberId = allInitiators[i]
                    local xPlayer = ESX.GetPlayerFromId(memberId)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerMember)
                        xPlayer.showNotification(_U('fight_end', rewardPerMember))
                        print("DEBUG: 给发起方成员", memberId, "发放奖励", rewardPerMember)
                    end
                end
            else
                print("DEBUG: 接受方获胜（玩家掉线），开始分配奖励")

                -- 给所有参与的接受方成员分配奖励（包括被击毙的）
                for i = 1, #allAcceptors do
                    local memberId = allAcceptors[i]
                    local xPlayer = ESX.GetPlayerFromId(memberId)
                    if xPlayer then
                        xPlayer.addAccountMoney('black_money', rewardPerMember)
                        xPlayer.showNotification(_U('fight_end', rewardPerMember))
                        print("DEBUG: 给接受方成员", memberId, "发放奖励", rewardPerMember)
                    end
                end
            end

            -- 无论哪方获胜，都设置冷却时间
            cds[fighting] = (os.time() + Config.fightZones[fighting].cd)
            TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, _U('fight_chat_end_content', fighting, winner))
            fighting = false
            fightSource = {}
            allInitiators = {} -- 清空记录
            allAcceptors = {} -- 清空记录
            mainSource = nil
            break
        end
    end
end)

ESX.RegisterServerCallback('Rs_gangfight:getscores', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)

    -- 检查玩家是否存在
    if xPlayer then
        local playerId = xPlayer.getIdentifier()

        -- 查询数据库以获取分数数据
        MySQL.Async.fetchAll('SELECT * FROM rs_gangfight WHERE owner = @playerId', {
            ['@playerId'] = playerId
        }, function(result)
            local scores = {}

            for _, row in pairs(result) do
                table.insert(scores, {
                    name = row.name,
                    mark = row.mark
                })
            end

            -- 返回分数数据给客户端
            cb(scores)
        end)
    else
        print('错误: 未找到玩家')
        cb(nil)
    end
end)


function Sanitize(str)
  local replacements = {
    ['&'] = '&amp;',
    ['<'] = '&lt;',
    ['>'] = '&gt;',
    ['\n'] = '<br/>'
  }

  return str
      :gsub('[&<>\n]', replacements)
      :gsub(' +', function(s)
        return ' ' .. ('&nbsp;'):rep(#s - 1)
      end)
end

-- 测试命令：模拟帮派斗争成功
RegisterCommand('testgangfight', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    -- 检查是否有正在进行的帮派斗争
    if fighting then
        TriggerClientEvent('esx:showNotification', source, '已有帮派斗争正在进行中')
        return
    end

    -- 获取斗争地点名称，默认为第一个地点
    local zoneName = args[1] or '格罗夫街地盘'
    if not Config.fightZones[zoneName] then
        TriggerClientEvent('esx:showNotification', source, '斗争地点不存在: ' .. zoneName)
        return
    end

    print("DEBUG: 开始测试帮派斗争 - 地点:", zoneName, "玩家:", source)

    -- 模拟开始帮派斗争
    fighting = zoneName
    fightSource['initiator'] = {source}
    allInitiators = {source}
    mainSource = source

    print("DEBUG: 测试帮派斗争设置完成")
    print("DEBUG: fightSource['initiator']:", fightSource['initiator'][1])
    print("DEBUG: allInitiators:", allInitiators[1])

    TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, '测试帮派斗争开始: ' .. zoneName)
    TriggerClientEvent('Rs_gangfight:allStart', -1, zoneName, {source})

    -- 3秒后模拟帮派斗争成功
    Citizen.SetTimeout(3000, function()
        if fighting == zoneName then
            print("DEBUG: 模拟帮派斗争成功")

            -- 保存allInitiators的副本
            local savedInitiators = {}
            for i = 1, #allInitiators do
                table.insert(savedInitiators, allInitiators[i])
            end

            TriggerClientEvent('Rs_gangfight:allEnd', -1)
            local winner = '发起方'

            -- 计算奖励金额
            local rewardPerMember = math.random(Config.fightZones[zoneName].money1, Config.fightZones[zoneName].money2)

            print("DEBUG: 测试帮派斗争成功，开始分配奖励")
            print("DEBUG: savedInitiators数量:", #savedInitiators)
            print("DEBUG: 每人奖励金额:", rewardPerMember)

            -- 给所有参与的发起方成员分配奖励
            for i = 1, #savedInitiators do
                local memberId = savedInitiators[i]
                local xPlayer = ESX.GetPlayerFromId(memberId)
                print("DEBUG: 处理发起方成员ID:", memberId, "xPlayer存在:", xPlayer ~= nil)
                if xPlayer then
                    xPlayer.addAccountMoney('black_money', rewardPerMember)
                    xPlayer.showNotification(_U('fight_end', rewardPerMember))
                    print("DEBUG: 给发起方成员", memberId, "发放奖励", rewardPerMember)
                end
            end

            cds[zoneName] = (os.time() + Config.fightZones[zoneName].cd)
            TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, '测试帮派斗争结束: ' .. zoneName .. ' 获胜方: ' .. winner)

            fighting = false
            fightSource = {}
            allInitiators = {}
            mainSource = nil
        end
    end)

    TriggerClientEvent('esx:showNotification', source, '测试帮派斗争已开始，3秒后自动成功')
end, false)

-- 测试命令：模拟被击毙后获得奖励
RegisterCommand('testgangfightdie', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    -- 检查是否有正在进行的帮派斗争
    if fighting then
        TriggerClientEvent('esx:showNotification', source, '已有帮派斗争正在进行中')
        return
    end

    -- 获取斗争地点名称，默认为第一个地点
    local zoneName = args[1] or '格罗夫街地盘'
    if not Config.fightZones[zoneName] then
        TriggerClientEvent('esx:showNotification', source, '斗争地点不存在: ' .. zoneName)
        return
    end

    print("DEBUG: 开始测试帮派斗争(被击毙) - 地点:", zoneName, "玩家:", source)

    -- 模拟开始帮派斗争
    fighting = zoneName
    fightSource['initiator'] = {source}
    fightSource['acceptor'] = {999} -- 模拟接受方
    allInitiators = {source}
    mainSource = source

    TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, '测试帮派斗争开始(被击毙测试): ' .. zoneName)
    TriggerClientEvent('Rs_gangfight:allStart', -1, zoneName, {source})

    -- 3秒后模拟发起方被击毙，但接受方也全部阵亡，发起方获胜
    Citizen.SetTimeout(3000, function()
        if fighting == zoneName then
            print("DEBUG: 模拟发起方被击毙但最终获胜")

            -- 先移除发起方成员（模拟被击毙）
            for i = 1, #fightSource['initiator'] do
                if fightSource['initiator'][i] == source then
                    table.remove(fightSource['initiator'], i)
                    break
                end
            end

            -- 然后移除所有接受方成员（模拟接受方全部阵亡）
            fightSource['acceptor'] = {}

            -- 保存allInitiators的副本
            local savedInitiators = {}
            for i = 1, #allInitiators do
                table.insert(savedInitiators, allInitiators[i])
            end

            TriggerClientEvent('Rs_gangfight:allEnd', -1)
            local winner = '发起方'

            -- 计算奖励金额
            local rewardPerMember = math.random(Config.fightZones[zoneName].money1, Config.fightZones[zoneName].money2)

            print("DEBUG: 测试帮派斗争(被击毙)成功，开始分配奖励")
            print("DEBUG: savedInitiators数量:", #savedInitiators)
            print("DEBUG: 每人奖励金额:", rewardPerMember)

            -- 给所有参与的发起方成员分配奖励（包括被击毙的）
            for i = 1, #savedInitiators do
                local memberId = savedInitiators[i]
                local xPlayer = ESX.GetPlayerFromId(memberId)
                print("DEBUG: 处理发起方成员ID:", memberId, "xPlayer存在:", xPlayer ~= nil)
                if xPlayer then
                    xPlayer.addAccountMoney('black_money', rewardPerMember)
                    xPlayer.showNotification(_U('fight_end', rewardPerMember))
                    print("DEBUG: 给发起方成员", memberId, "发放奖励", rewardPerMember)
                end
            end

            cds[zoneName] = (os.time() + Config.fightZones[zoneName].cd)
            TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, '测试帮派斗争结束(被击毙测试): ' .. zoneName .. ' 获胜方: ' .. winner)

            fighting = false
            fightSource = {}
            allInitiators = {}
            mainSource = nil
        end
    end)

    TriggerClientEvent('esx:showNotification', source, '测试帮派斗争(被击毙)已开始，3秒后模拟被击毙但获得奖励')
end, false)

-- 检查玩家职业状态的命令
RegisterCommand('checkgang', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        print("无法获取玩家信息")
        return
    end

    local jobName = xPlayer.job.name
    local jobLabel = xPlayer.job.label
    local isGang = IsGangMember(xPlayer)

    print("=== 玩家职业信息 ===")
    print("玩家ID:", source)
    print("职业名称:", jobName)
    print("职业标签:", jobLabel)
    print("是否帮派成员:", isGang)
    print("配置的帮派职业:", json.encode(Config.GangJobs))

    TriggerClientEvent('esx:showNotification', source, '职业: ' .. jobLabel .. ' (' .. jobName .. ') | 帮派成员: ' .. (isGang and '是' or '否'))
end, false)

-- 查看在线帮派成员的命令
RegisterCommand('ganglist', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local xPlayers = ESX.GetPlayers()
    local gangCount = {}
    local playerGang = GetPlayerGang(xPlayer)

    print("=== 在线帮派成员统计 ===")
    print("您的帮派:", playerGang or "无")

    for i=1, #xPlayers, 1 do
        local otherPlayer = ESX.GetPlayerFromId(xPlayers[i])
        if otherPlayer ~= nil then
            local otherGang = GetPlayerGang(otherPlayer)
            if otherGang then
                if not gangCount[otherGang] then
                    gangCount[otherGang] = 0
                end
                gangCount[otherGang] = gangCount[otherGang] + 1
                print("玩家ID " .. xPlayers[i] .. " - 帮派: " .. otherGang)
            end
        end
    end

    print("帮派统计:")
    for gang, count in pairs(gangCount) do
        print(gang .. ": " .. count .. "人")
    end

    -- 计算其他帮派成员数量
    local otherGangMembers = 0
    for gang, count in pairs(gangCount) do
        if gang ~= playerGang then
            otherGangMembers = otherGangMembers + count
        end
    end

    print("其他帮派成员总数:", otherGangMembers)
    TriggerClientEvent('esx:showNotification', source, '其他帮派在线成员: ' .. otherGangMembers .. '人 (查看控制台获取详细信息)')
end, false)

-- 清除当前斗争状态的命令（管理员用）
RegisterCommand('clearfight', function(source, args, rawCommand)
    if fighting then
        print("DEBUG: 管理员清除斗争状态 - 地点:", fighting)
        TriggerClientEvent('Rs_gangfight:allEnd', -1)
        TriggerClientEvent('chatMessage', -1, _U('fight_chat_title'), {255, 0, 0}, '管理员已清除当前帮派斗争')
        fighting = false
        fightSource = {}
        allInitiators = {}
        mainSource = nil
        TriggerClientEvent('esx:showNotification', source, '已清除当前帮派斗争状态')
    else
        TriggerClientEvent('esx:showNotification', source, '当前没有进行中的帮派斗争')
    end
end, false)