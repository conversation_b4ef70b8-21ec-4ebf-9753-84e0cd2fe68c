
CREATE TABLE IF NOT EXISTS `rs_gangfight` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner` varchar(60) NOT NULL,
  `name` varchar(255) NOT NULL,
  `mark` int(11) NOT NULL DEFAULT 0,
  `gang` varchar(50) DEFAULT NULL,
  `zone` varchar(100) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `owner` (`owner`),
  KEY `gang` (`gang`),
  KEY `zone` (`zone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



-- 添加一些示例数据（可选）
-- INSERT INTO rs_gangfight (owner, name, mark, gang, zone) VALUES
-- ('steam:1234567890abcdef', '测试玩家1', 5, 'ballas', '格罗夫街地盘'),
-- ('steam:abcdef1234567890', '测试玩家2', 3, 'families', '巴拉斯地盘');
