local ESX = nil
if Config.oldESX then
    --TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)
    ESX = exports["es_extended"]:getSharedObject()
    RegisterNetEvent('esx:playerLoaded')
    AddEventHandler('esx:playerLoaded', function(xPlayer)
        ESX.PlayerData = xPlayer
        PlayerLoaded = true
    end)
end

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)

Citizen.CreateThread(function()
	for k,v in pairs(Config.fightZones) do
	    local blip = AddBlipForCoord(v.coords)
		SetBlipSprite(blip, 84)  -- 使用帮派图标
        SetBlipColour(blip, 1)
		SetBlipScale(blip, 0.8)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentSubstringPlayerName('帮派｜斗争')
		EndTextCommandSetBlipName(blip)
	end
end)

-- 检查玩家是否是帮派成员
function IsGangMember()
    if not ESX.PlayerData or not ESX.PlayerData.job then
        return false
    end

    for _, job in ipairs(Config.GangJobs) do
        if ESX.PlayerData.job.name == job then
            return true
        end
    end
    return false
end

-- 获取玩家的帮派名称
function GetPlayerGang()
    if not ESX.PlayerData or not ESX.PlayerData.job then
        return nil
    end

    for _, job in ipairs(Config.GangJobs) do
        if ESX.PlayerData.job.name == job then
            return job
        end
    end
    return nil
end


Citizen.CreateThread(function()
    while not ESX.PlayerData do Citizen.Wait(100) end
    while not ESX.PlayerData.job do Citizen.Wait(100) end
    while true do
        Citizen.Wait(0)
        local wake = false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        if not holdingup then
            for k, v in pairs(Config.fightZones) do
                local dist = Vdist(coords, v.coords)
                if dist < Config.DrawDistance then
                    -- 所有玩家都能看到标记
                    DrawMarker(Config.marker.type, v.coords.x, v.coords.y, v.coords.z - 1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.marker.x, Config.marker.y, Config.marker.z, Config.marker.r, Config.marker.g, Config.marker.b, 100, false, false, 2, false, false, false, false)
                    wake = true
                    if dist < Config.marker.x then
                        if IsGangMember() then
                            -- 只有帮派成员才能看到交互提示
                            ESX.ShowHelpNotification(_U('fight_help'))
                            if IsControlJustReleased(0, 38) then
                                if IsPedArmed(ped, 4) then
                                    ESX.TriggerServerCallback('Rs_gangfight:requestStart', function(cb)
                                        if cb then
                                            ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'initiator_id', {
                                                title = _U('initiator_id')
                                                }, function(data, menu)
                                                    local input = tostring(GetPlayerServerId(PlayerId())) -- 自己的ID
                                                    if data.value ~= nil and data.value ~= '' and data.value ~= 'nil' then
                                                        input = input..','..data.value -- 添加队友ID
                                                    end
                                                    ESX.TriggerServerCallback('Rs_gangfight:checkId', function(cb)
                                                        if cb then
                                                            menu.close()
                                                            holdingup = true
                                                            current = k
                                                            involved = true
                                                        end
                                                    end, input, k)
                                            end, function(data, menu)
                                                menu.close()
                                            end)
                                        end
                                    end, k)
                                else
                                    ESX.ShowNotification(_U('not_armed'))
                                end
                            end
                        else
                            -- 非帮派成员的提示
                            ESX.ShowHelpNotification('~r~只有帮派成员才能发起斗争')
                        end
                    end
                    break
                end
            end
                else
            local dist = Vdist(coords, Config.fightZones[current].coords)
            if dist > Config.fightZones[current].maxDist then
                TriggerServerEvent('Rs_gangfight:earlyExit')
                --ESX.ShowNotification(_U('early_exit'))
            end
        end
        if not wake then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('Rs_gangfight:allStart')
AddEventHandler('Rs_gangfight:allStart', function(zone, ids)
    CreateBlip(Config.fightZones[zone].coords)
    Citizen.CreateThread(function()
        awaitinginput = true
        while IsGangMember() and awaitinginput do
            Citizen.Wait(0)
            ESX.ShowHelpNotification(_U('acceptor_input_id'))
            if IsControlJustReleased(0, 38) then
                ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'acceptor_id', {
                    title = _U('acceptor_id')
                    }, function(data, menu)
                        if awaitinginput then
                            if data.value == nil then
                                ESX.ShowNotification(_U('invalid_input'))
                            else
                                TriggerServerEvent('Rs_gangfight:acceptorId', data.value)
                                menu.close()
                            end

                        else
                            ESX.ShowNotification(_U('input_done'))
                            menu.close()
                        end
                end, function(data, menu)
                    menu.close()
                end)
            end
        end
    end)
    local id = GetPlayerServerId(PlayerId())
    for i = 1, #ids do
        if ids[i] == id then
            involved = true
            break
        end
    end
    current = zone
    local reported = false
    while current == zone do
        Citizen.Wait(0)
        local wake = false
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local dist = Vdist(coords, Config.fightZones[zone].coords)
        if dist < Config.DrawDistance2 then
            wake = true
            DrawMarker(Config.marker.type, Config.fightZones[zone].coords.x, Config.fightZones[zone].coords.y, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, Config.fightZones[zone].maxDist * 2, Config.fightZones[zone].maxDist * 2, 255.0, Config.marker.r, Config.marker.g, Config.marker.b, 100, false, false, 2, false, false, false, false)
            if involved then
				if IsEntityDead(ped) and not reported then
					local kPed = GetPedSourceOfDeath(ped)
					local killer = 0
					local kPlayer = NetworkGetPlayerIndexFromPed(kPed)
					if kPlayer ~= PlayerId() then
						if (kPlayer ~= nil or kPlayer ~= -1) and kPed ~= 0 and IsEntityAPed(kPed) and IsEntityAPed(GetPedInVehicleSeat(kPed, -1)) and IsPedAPlayer(GetPedInVehicleSeat(kPed, -1)) then
							killer = NetworkGetPlayerIndexFromPed(GetPedInVehicleSeat(kPed, -1))
						else
							killer = NetworkGetPlayerIndexFromPed(kPed)
						end
						TriggerServerEvent("Rs_gangfight:out",GetPlayerServerId(killer))
					end
					checkcopdie = false
				end
            elseif dist < Config.fightZones[zone].maxDist then
                local vehicle = GetVehiclePedIsIn(ped, false)
                if vehicle ~= 0 then
                    SetEntityCoords(vehicle, Config.safepos)
                else
                    --SetEntityCoords(ped, Config.safepos)
               end
            end
        end
        if not wake then
            Citizen.Wait(1000)
        end
    end
end)

RegisterNetEvent('Rs_gangfight:acceptorIdEnd')
AddEventHandler('Rs_gangfight:acceptorIdEnd', function(ids)
    awaitinginput = false
    local id = GetPlayerServerId(PlayerId())
    for i = 1, #ids do
        if ids[i] == tonumber(id) then
            involved = true
            break
        end
    end
end)

RegisterNetEvent('Rs_gangfight:allEnd')
AddEventHandler('Rs_gangfight:allEnd', function()
    KillBlip()
    current = nil
    involved = false
    holdingup = false
    awaitinginput = false
end)

-- Apple
local scores = {}
local RACING_HUD_COLOR = {238, 198, 78, 255}
local raceScoreColors = {
    {255, 140, 0, 255},
    {255, 20, 147, 255},
    {100, 149, 237, 255}
}

-- RegisterNetEvent('esx:playerLoaded')
-- AddEventHandler('esx:playerLoaded', function(xPlayer)
	-- ESX.TriggerServerCallback("Rs_gangfight:getscores", function(getscores)
		-- scores = getscores
	-- end)

	-- CreateThread(function()
		-- while true do
			-- local sleep = 2000
			-- local player = PlayerPedId()
			-- if GetDistanceBetweenCoords( Config.showscores.x, Config.showscores.y, Config.showscores.z, GetEntityCoords(player)) < 15.0 then
				-- -- 显示排行榜标题
				-- sleep = 0
				-- Draw3DText(Config.showscores.x, Config.showscores.y, Config.showscores.z-0.600, '~h~~r~帮派斗争排行榜', RACING_HUD_COLOR, 4, 0.3, 0.3)
			-- end

			-- -- 当足够接近时，显示分数
			-- if GetDistanceBetweenCoords(Config.showscores.x, Config.showscores.y, Config.showscores.z, GetEntityCoords(player)) < 15.0 then
			-- -- 如果我们收到了更新的分数，请显示它们
			-- local count = 0
			-- drawScores = {}
			-- for i = 1, #scores, 1 do
				-- table.insert(drawScores, scores[i].name..' | ~h~~w~【人头】: '..scores[i].mark)
			-- end

			-- -- 初始化偏移
			-- local zOffset = 0
			-- if (#drawScores > #raceScoreColors) then
				-- zOffset = 0.450*(#raceScoreColors) + 0.300*(#drawScores - #raceScoreColors - 1)
			-- else
				-- zOffset = 0.450*(#drawScores - 1)
			-- end

			-- -- 打印标题上方的分数
			-- for i = 1, #drawScores, 1 do
				-- -- 绘制带有颜色编码的分数文本
				-- if (i > #raceScoreColors) then
					-- -- 以白色绘制分数，递减偏移
					-- Draw3DText(Config.showscores.x, Config.showscores.y, Config.showscores.z+zOffset, drawScores[i], {255,255,255,255}, 4, 0.13, 0.13)
					-- zOffset = zOffset - 0.300
				-- else
					-- -- 用颜色和较大的文本绘制分数，减少偏移量
					-- Draw3DText(Config.showscores.x, Config.showscores.y, Config.showscores.z+zOffset, drawScores[i], raceScoreColors[i], 4, 0.22, 0.22)
					-- zOffset = zOffset - 0.450
				-- end
			-- end
			-- end
			-- Wait(sleep)
		-- end
	-- end)
-- end)

-- 显示三维文本的实用程序功能
function Draw3DText(x,y,z,textInput,colour,fontId,scaleX,scaleY)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px,py,pz, x,y,z, 1)
    local scale = (1/dist)*20
    local fov = (1/GetGameplayCamFov())*100
    local scale = scale*fov

    SetTextScale(scaleX*scale, scaleY*scale)
    SetTextFont(0)
    SetTextProportional(1)
    local colourr,colourg,colourb,coloura = table.unpack(colour)
    SetTextColour(colourr,colourg,colourb, coloura)
    SetTextDropshadow(2, 1, 1, 1, 255)
    SetTextEdge(3, 0, 0, 0, 150)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(textInput)
    SetDrawOrigin(x,y,z+2, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()
end

RegisterNetEvent('Rs_gangfight:refersh')
AddEventHandler('Rs_gangfight:refersh', function(newscores)
	scores = newscores
end)

-- MakeBlip = function(pos, addshop)
	-- local blip = AddBlipForCoord(pos.x,pos.y,pos.z)
	-- SetBlipSprite(blip,Config.BlipSprite)
	-- SetBlipDisplay(blip,2)
	-- SetBlipScale(blip,1.0)
	-- SetBlipColour(blip,Config.BlipColor)
	-- SetBlipDisplay(blip,4)
	-- SetBlipAsShortRange(blip,true)
	-- SetBlipHighDetail(blip,true)
	-- BeginTextCommandSetBlipName("STRING")
	-- AddTextComponentString('抢劫中')
	-- EndTextCommandSetBlipName(blip)
	-- local blip2 = AddBlipForRadius(pos.x,pos.y,pos.z, Config.radius) -- 安全區範圍
	-- SetBlipHighDetail(blip2, false)
	-- SetBlipColour(blip2, 77)
	-- SetBlipAlpha (blip2, 128) -- 加深顏色
	-- CreateThread(function()
		-- local cacheshop = addshop
		-- local pos = pos
		-- while blipRobbery[cacheshop] do
			-- local plyPos = GetEntityCoords(PlayerPedId())
			-- local dist = Vdist(plyPos,pos)
			-- local sleep = 1000
			-- if dist < Config.radius + 300.0 then
				-- sleep = 0
				-- DrawMarker(28, pos, 0, 0, 0, 0, 0, 0, 250.0, 250.0, 250.0, 255, 0, 0, 50, 0, 0, 2, 0, 0, 0, 0)
			-- end
			-- Wait(sleep)
		-- end
	-- end)

	-- return blip,blip2
-- end