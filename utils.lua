function mysplit (inputstr, sep)
        if sep == nil then
                sep = "%s"
        end
        local t={}
        for str in string.gmatch(inputstr, "([^"..sep.."]+)") do
                table.insert(t, str)
        end
        return t
    end
    
    function CreateBlip(coords)
            blipGangFight = AddBlipForCoord(coords)

            SetBlipSprite(blipGangFight, 161)  -- 使用战斗图标
            SetBlipScale(blipGangFight, 2.0)
            SetBlipColour(blipGangFight, 1)    -- 红色表示帮派斗争

            PulseBlip(blipGangFight)
    end

    function KillBlip()
            if blipGangFight then
                    RemoveBlip(blipGangFight)
            end
    end